2025-06-05 20:11:11,724 - INFO - [MainThread] - Dhan.py  started system
2025-06-05 20:11:11,724 - INFO - [MainThread] - STARTED THE PROGRAM
2025-06-05 20:11:15,538 - INFO - [MainThread] - 🚀 Starting Multi-Timeframe Live Data Fetcher...
2025-06-05 20:11:15,538 - INFO - [MainThread] - 📊 Timeframes: 1min, 5min, 60min
2025-06-05 20:11:15,538 - INFO - [MainThread] - 📈 Symbol: NIFTY (INDEX)
2025-06-05 20:11:15,538 - INFO - [MainThread] - ⏰ Market Hours: 09:15:00 - 15:30:00 IST
2025-06-05 20:11:15,539 - INFO - [TF_0] - [1min] Starting timeframe worker...
2025-06-05 20:11:15,539 - INFO - [MainThread] - ✅ Started worker for 1min
2025-06-05 20:11:15,539 - INFO - [TF_1] - [5min] Starting timeframe worker...
2025-06-05 20:11:15,539 - INFO - [MainThread] - ✅ Started worker for 5min
2025-06-05 20:11:15,539 - INFO - [TF_2] - [60min] Starting timeframe worker...
2025-06-05 20:11:15,540 - INFO - [MainThread] - ✅ Started worker for 60min
2025-06-05 20:11:15,551 - INFO - [TF_2] - [60min] Loaded 22 historical candles into memory
2025-06-05 20:11:15,552 - INFO - [TF_2] - [60min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:15:00
2025-06-05 20:11:15,562 - INFO - [TF_1] - [5min] Loaded 226 historical candles into memory
2025-06-05 20:11:15,563 - INFO - [TF_1] - [5min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:30:00
2025-06-05 20:11:15,567 - INFO - [TF_2] - [60min] Found existing live file with 8 candles from today
2025-06-05 20:11:15,568 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:11:15 IST
2025-06-05 20:11:15,594 - INFO - [TF_1] - [5min] Found existing live file with 76 candles from today
2025-06-05 20:11:15,599 - INFO - [TF_0] - [1min] Loaded 1126 historical candles into memory
2025-06-05 20:11:15,599 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:11:15 IST
2025-06-05 20:11:15,599 - INFO - [TF_0] - [1min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:32:00
2025-06-05 20:11:15,602 - INFO - [TF_0] - [1min] Found existing live file with 376 candles from today
2025-06-05 20:11:15,602 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:11:15 IST
2025-06-05 20:12:15,579 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:12:15 IST
2025-06-05 20:12:15,604 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:12:15 IST
2025-06-05 20:12:15,604 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:12:15 IST
2025-06-05 20:12:40,315 - INFO - [MainThread] - 🛑 Received keyboard interrupt, shutting down all workers...
2025-06-05 20:12:40,316 - INFO - [MainThread] - 🔚 Multi-timeframe data collection ended
2025-06-05 20:13:15,582 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:13:15 IST
2025-06-05 20:13:15,609 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:13:15 IST
2025-06-05 20:13:15,609 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:13:15 IST
2025-06-05 20:14:15,583 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:14:15 IST
2025-06-05 20:14:15,611 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:14:15 IST
2025-06-05 20:14:15,611 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:14:15 IST
2025-06-05 20:14:40,433 - INFO - [MainThread] - Application terminated
2025-06-05 20:48:16,558 - INFO - [MainThread] - Dhan.py  started system
2025-06-05 20:48:16,558 - INFO - [MainThread] - STARTED THE PROGRAM
2025-06-05 20:48:20,339 - INFO - [MainThread] - 🚀 Starting Multi-Timeframe Live Data Fetcher...
2025-06-05 20:48:20,340 - INFO - [MainThread] - 📊 Timeframes: 1min, 5min, 60min
2025-06-05 20:48:20,340 - INFO - [MainThread] - 📈 Symbol: HDFC (NSE_EQ)
2025-06-05 20:48:20,340 - INFO - [MainThread] - ⏰ Market Hours: 09:15:00 - 15:30:00 IST
2025-06-05 20:48:20,340 - INFO - [TF_0] - [1min] Starting timeframe worker...
2025-06-05 20:48:20,340 - INFO - [MainThread] - ✅ Started worker for 1min
2025-06-05 20:48:20,341 - INFO - [TF_1] - [5min] Starting timeframe worker...
2025-06-05 20:48:20,341 - INFO - [MainThread] - ✅ Started worker for 5min
2025-06-05 20:48:20,343 - INFO - [TF_2] - [60min] Starting timeframe worker...
2025-06-05 20:48:20,344 - INFO - [MainThread] - ✅ Started worker for 60min
2025-06-05 20:48:20,346 - INFO - [TF_0] - [1min] Created new live file: Live_Data_Layer/HDFC_NSE_EQ_1min_live.csv
2025-06-05 20:48:20,346 - INFO - [TF_1] - [5min] Created new live file: Live_Data_Layer/HDFC_NSE_EQ_5min_live.csv
2025-06-05 20:48:20,347 - INFO - [TF_2] - [60min] Created new live file: Live_Data_Layer/HDFC_NSE_EQ_60min_live.csv
2025-06-05 20:48:20,363 - INFO - [TF_2] - [60min] Loaded 22 historical candles into memory
2025-06-05 20:48:20,365 - INFO - [TF_2] - [60min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:15:00
2025-06-05 20:48:20,379 - INFO - [TF_1] - [5min] Loaded 226 historical candles into memory
2025-06-05 20:48:20,379 - INFO - [TF_1] - [5min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:30:00
2025-06-05 20:48:20,382 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:48:20 IST
2025-06-05 20:48:20,382 - INFO - [TF_2] - [60min] 🔄 Fetching today's data for live file...
2025-06-05 20:48:20,383 - INFO - [TF_2] - [60min] Fetching today's data for live file...
2025-06-05 20:48:20,396 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:48:20 IST
2025-06-05 20:48:20,415 - INFO - [TF_1] - [5min] 🔄 Fetching today's data for live file...
2025-06-05 20:48:20,417 - INFO - [TF_0] - [1min] Loaded 1126 historical candles into memory
2025-06-05 20:48:20,417 - INFO - [TF_1] - [5min] Fetching today's data for live file...
2025-06-05 20:48:20,425 - INFO - [TF_0] - [1min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:32:00
2025-06-05 20:48:20,428 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:48:20 IST
2025-06-05 20:48:20,433 - INFO - [TF_0] - [1min] 🔄 Fetching today's data for live file...
2025-06-05 20:48:20,433 - INFO - [TF_0] - [1min] Fetching today's data for live file...
2025-06-05 20:48:20,447 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:20,455 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 1
2025-06-05 20:48:20,455 - INFO - [TF_2] - [60min] Retrying in 2 seconds...
2025-06-05 20:48:22,503 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:22,509 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 2
2025-06-05 20:48:22,509 - INFO - [TF_2] - [60min] Retrying in 2 seconds...
2025-06-05 20:48:24,551 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:24,556 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 3
2025-06-05 20:48:24,556 - ERROR - [TF_2] - [60min] All 3 API call attempts failed
2025-06-05 20:48:24,557 - WARNING - [TF_2] - [60min] No data received from API
2025-06-05 20:48:24,588 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:24,592 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 1
2025-06-05 20:48:24,592 - INFO - [TF_1] - [5min] Retrying in 2 seconds...
2025-06-05 20:48:26,654 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:26,660 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 2
2025-06-05 20:48:26,660 - INFO - [TF_1] - [5min] Retrying in 2 seconds...
2025-06-05 20:48:28,713 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:28,719 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 3
2025-06-05 20:48:28,719 - ERROR - [TF_1] - [5min] All 3 API call attempts failed
2025-06-05 20:48:28,720 - WARNING - [TF_1] - [5min] No data received from API
2025-06-05 20:48:28,750 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:28,755 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 1
2025-06-05 20:48:28,756 - INFO - [TF_0] - [1min] Retrying in 2 seconds...
2025-06-05 20:48:30,818 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:30,825 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 2
2025-06-05 20:48:30,825 - INFO - [TF_0] - [1min] Retrying in 2 seconds...
2025-06-05 20:48:32,886 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:48:32,894 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 3
2025-06-05 20:48:32,894 - ERROR - [TF_0] - [1min] All 3 API call attempts failed
2025-06-05 20:48:32,895 - WARNING - [TF_0] - [1min] No data received from API
2025-06-05 20:48:55,412 - INFO - [MainThread] - 🛑 Received keyboard interrupt, shutting down all workers...
2025-06-05 20:48:55,412 - INFO - [MainThread] - 🔚 Multi-timeframe data collection ended
2025-06-05 20:48:56,656 - INFO - [MainThread] - Application terminated
2025-06-05 20:49:00,790 - INFO - [MainThread] - Dhan.py  started system
2025-06-05 20:49:00,790 - INFO - [MainThread] - STARTED THE PROGRAM
2025-06-05 20:49:05,019 - INFO - [MainThread] - 🚀 Starting Multi-Timeframe Live Data Fetcher...
2025-06-05 20:49:05,019 - INFO - [MainThread] - 📊 Timeframes: 1min, 5min, 60min
2025-06-05 20:49:05,020 - INFO - [MainThread] - 📈 Symbol: HDFCBANK (NSE_EQ)
2025-06-05 20:49:05,020 - INFO - [MainThread] - ⏰ Market Hours: 09:15:00 - 15:30:00 IST
2025-06-05 20:49:05,020 - INFO - [TF_0] - [1min] Starting timeframe worker...
2025-06-05 20:49:05,020 - INFO - [MainThread] - ✅ Started worker for 1min
2025-06-05 20:49:05,021 - INFO - [TF_1] - [5min] Starting timeframe worker...
2025-06-05 20:49:05,021 - INFO - [MainThread] - ✅ Started worker for 5min
2025-06-05 20:49:05,024 - INFO - [TF_2] - [60min] Starting timeframe worker...
2025-06-05 20:49:05,024 - INFO - [MainThread] - ✅ Started worker for 60min
2025-06-05 20:49:05,024 - INFO - [TF_1] - [5min] Created new live file: Live_Data_Layer/HDFCBANK_NSE_EQ_5min_live.csv
2025-06-05 20:49:05,024 - INFO - [TF_0] - [1min] Created new live file: Live_Data_Layer/HDFCBANK_NSE_EQ_1min_live.csv
2025-06-05 20:49:05,027 - INFO - [TF_2] - [60min] Created new live file: Live_Data_Layer/HDFCBANK_NSE_EQ_60min_live.csv
2025-06-05 20:49:05,052 - INFO - [TF_2] - [60min] Loaded 22 historical candles into memory
2025-06-05 20:49:05,048 - INFO - [TF_1] - [5min] Loaded 226 historical candles into memory
2025-06-05 20:49:05,053 - INFO - [TF_2] - [60min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:15:00
2025-06-05 20:49:05,053 - INFO - [TF_1] - [5min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:30:00
2025-06-05 20:49:05,057 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:49:05 IST
2025-06-05 20:49:05,058 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:49:05 IST
2025-06-05 20:49:05,058 - INFO - [TF_1] - [5min] 🔄 Fetching today's data for live file...
2025-06-05 20:49:05,058 - INFO - [TF_2] - [60min] 🔄 Fetching today's data for live file...
2025-06-05 20:49:05,058 - INFO - [TF_1] - [5min] Fetching today's data for live file...
2025-06-05 20:49:05,059 - INFO - [TF_2] - [60min] Fetching today's data for live file...
2025-06-05 20:49:05,100 - INFO - [TF_0] - [1min] Loaded 1126 historical candles into memory
2025-06-05 20:49:05,101 - INFO - [TF_0] - [1min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:32:00
2025-06-05 20:49:05,109 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:49:05 IST
2025-06-05 20:49:05,118 - INFO - [TF_0] - [1min] 🔄 Fetching today's data for live file...
2025-06-05 20:49:05,123 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:05,123 - INFO - [TF_0] - [1min] Fetching today's data for live file...
2025-06-05 20:49:05,128 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 1
2025-06-05 20:49:05,129 - INFO - [TF_1] - [5min] Retrying in 2 seconds...
2025-06-05 20:49:07,184 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:07,191 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 2
2025-06-05 20:49:07,191 - INFO - [TF_1] - [5min] Retrying in 2 seconds...
2025-06-05 20:49:09,254 - ERROR - [TF_1] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:09,260 - WARNING - [TF_1] - [5min] API call returned empty data on attempt 3
2025-06-05 20:49:09,260 - ERROR - [TF_1] - [5min] All 3 API call attempts failed
2025-06-05 20:49:09,261 - WARNING - [TF_1] - [5min] No data received from API
2025-06-05 20:49:09,293 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:09,297 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 1
2025-06-05 20:49:09,297 - INFO - [TF_2] - [60min] Retrying in 2 seconds...
2025-06-05 20:49:11,356 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:11,362 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 2
2025-06-05 20:49:11,362 - INFO - [TF_2] - [60min] Retrying in 2 seconds...
2025-06-05 20:49:13,424 - ERROR - [TF_2] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:13,431 - WARNING - [TF_2] - [60min] API call returned empty data on attempt 3
2025-06-05 20:49:13,431 - ERROR - [TF_2] - [60min] All 3 API call attempts failed
2025-06-05 20:49:13,431 - WARNING - [TF_2] - [60min] No data received from API
2025-06-05 20:49:13,463 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:13,467 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 1
2025-06-05 20:49:13,468 - INFO - [TF_0] - [1min] Retrying in 2 seconds...
2025-06-05 20:49:15,526 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:15,533 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 2
2025-06-05 20:49:15,533 - INFO - [TF_0] - [1min] Retrying in 2 seconds...
2025-06-05 20:49:17,588 - ERROR - [TF_0] - Exception in Getting OHLC data as 'NSE_EQ'
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 418, in get_historical_data
    exchange_segment = script_exchange[exchange]
                       ~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 'NSE_EQ'
2025-06-05 20:49:17,595 - WARNING - [TF_0] - [1min] API call returned empty data on attempt 3
2025-06-05 20:49:17,595 - ERROR - [TF_0] - [1min] All 3 API call attempts failed
2025-06-05 20:49:17,596 - WARNING - [TF_0] - [1min] No data received from API
2025-06-05 20:49:22,262 - INFO - [MainThread] - 🛑 Received keyboard interrupt, shutting down all workers...
2025-06-05 20:49:22,263 - INFO - [MainThread] - 🔚 Multi-timeframe data collection ended
2025-06-05 20:49:22,647 - INFO - [MainThread] - Application terminated
2025-06-05 20:49:38,491 - INFO - [MainThread] - Dhan.py  started system
2025-06-05 20:49:38,491 - INFO - [MainThread] - STARTED THE PROGRAM
2025-06-05 20:49:41,981 - INFO - [MainThread] - 🚀 Starting Multi-Timeframe Live Data Fetcher...
2025-06-05 20:49:41,981 - INFO - [MainThread] - 📊 Timeframes: 1min, 5min, 60min
2025-06-05 20:49:41,981 - INFO - [MainThread] - 📈 Symbol: NIFTY (INDEX)
2025-06-05 20:49:41,981 - INFO - [MainThread] - ⏰ Market Hours: 09:15:00 - 15:30:00 IST
2025-06-05 20:49:41,982 - INFO - [TF_0] - [1min] Starting timeframe worker...
2025-06-05 20:49:41,982 - INFO - [MainThread] - ✅ Started worker for 1min
2025-06-05 20:49:41,983 - INFO - [TF_1] - [5min] Starting timeframe worker...
2025-06-05 20:49:41,983 - INFO - [MainThread] - ✅ Started worker for 5min
2025-06-05 20:49:41,983 - INFO - [TF_2] - [60min] Starting timeframe worker...
2025-06-05 20:49:41,984 - INFO - [MainThread] - ✅ Started worker for 60min
2025-06-05 20:49:41,996 - INFO - [TF_2] - [60min] Loaded 22 historical candles into memory
2025-06-05 20:49:41,998 - INFO - [TF_2] - [60min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:15:00
2025-06-05 20:49:42,011 - INFO - [TF_1] - [5min] Loaded 226 historical candles into memory
2025-06-05 20:49:42,012 - INFO - [TF_1] - [5min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:30:00
2025-06-05 20:49:42,014 - INFO - [TF_2] - [60min] Found existing live file with 8 candles from today
2025-06-05 20:49:42,015 - INFO - [TF_2] - [60min] Market is closed at 2025-06-05 20:49:42 IST
2025-06-05 20:49:42,053 - INFO - [TF_0] - [1min] Loaded 1126 historical candles into memory
2025-06-05 20:49:42,054 - INFO - [TF_1] - [5min] Found existing live file with 76 candles from today
2025-06-05 20:49:42,054 - INFO - [TF_0] - [1min] Historical data range: 2025-06-03 09:15:00 to 2025-06-05 17:32:00
2025-06-05 20:49:42,055 - INFO - [TF_1] - [5min] Market is closed at 2025-06-05 20:49:42 IST
2025-06-05 20:49:42,056 - INFO - [TF_0] - [1min] Found existing live file with 376 candles from today
2025-06-05 20:49:42,057 - INFO - [TF_0] - [1min] Market is closed at 2025-06-05 20:49:42 IST
2025-06-05 20:50:30,454 - INFO - [MainThread] - 🛑 Received keyboard interrupt, shutting down all workers...
2025-06-05 20:50:30,454 - INFO - [MainThread] - 🔚 Multi-timeframe data collection ended
2025-06-05 20:50:30,752 - INFO - [MainThread] - Application terminated
